<?php

/**
 * Info Helper
 * Contains utility functions for handling common data operations
 */

function datetimeforms($date)
{
    if ($date == "0000-00-00") {
        echo "No Date Set";
    } else {
        if (!empty($date)) {
            $dtime = new DateTime($date);
            print $dtime->format("d M Y H:ia");
        } else {
            echo "-";
        }
    }
}

function dateforms($date)
{
    if ($date == "0000-00-00") {
        echo "No Date Set";
    } else {

        if (!empty($date)) {
            $dtime = new DateTime($date);

            print $dtime->format("d M Y");
        } else {
            echo "-";
        }
    }
}

function getAge($date)
{
    if (!empty($date)) {
        $dateOfBirth = $date;
        $today = date("Y-m-d");
        $diff = date_diff(date_create($dateOfBirth), date_create($today));
        $age = $diff->format('%y');
        if ($age == date("Y")) {
            echo "--";
        } else {
            return $age;
        }
    } else {
        echo "-";
    }
}

function getDateAgo($date)
{
    if (!empty($date)) {
        $dateAgo = round((strtotime(date("Y-m-d H:i:s")) - strtotime($date)) / 86400);
        echo $dateAgo;
    } else {
        echo "-";
    }
}

function minstoexpire($date){
     //expire date count down
     $future_date = new DateTime($date);
     $current_date = new DateTime();

     // calculate the time difference between the two dates
     $time_diff = $future_date->diff($current_date);

     // extract the remaining days, hours, minutes, and seconds from the time difference
     $days = $time_diff->days;
     $hours = $time_diff->h;
     $minutes = $time_diff->i;
     $seconds = $time_diff->s;

     return $minutes;
     // output the remaining time

}

if (!function_exists('imgcheck')) {
    /**
     * Check if an image exists and return the path or a default image
     *
     * @param string $img_path The image path to check
     * @param string|null $default_img Optional default image path
     * @return string The image path or default image path
     */
    function imgcheck($img_path, $default_img = null) {
        // Set the default fallback image
        $fallback_img = base_url('assets/system_img/no-img.jpg');

        // If input is empty, return the default or fallback
        if (empty($img_path)) {
            return $default_img ?? $fallback_img;
        }

        // For external images (with http/https), just return them
        if (strpos($img_path, 'http') === 0) {
            return $img_path;
        }

        // Normalize the path - remove 'public/' prefix if present since FCPATH already points to public
        $normalized_path = $img_path;
        if (strpos($normalized_path, 'public/') === 0) {
            $normalized_path = substr($normalized_path, 7); // Remove 'public/' prefix
        }

        // If the path doesn't start with a slash, add it for consistency
        if (strpos($normalized_path, '/') !== 0) {
            $normalized_path = '/' . $normalized_path;
        }

        // For local images, check if they exist in the public directory
        $path = parse_url($normalized_path, PHP_URL_PATH);
        $full_path = FCPATH . ltrim($path, '/');

        if (file_exists($full_path)) {
            // Return the URL without 'public/' prefix since base_url() handles that
            return base_url(ltrim($normalized_path, '/'));
        }

        // Return the default if the image doesn't exist
        return $default_img ?? $fallback_img;
    }
}

function getfileExtension($filepath){

    if(!empty($filepath)){
        echo $fileExt = pathinfo($filepath,PATHINFO_EXTENSION);
    }else{
        echo "No File";
    }

}


function removeCommaWithEmptySpace($string) {
    $result = str_replace(', ', ' ', $string);
    $result = str_replace(',,', ' ', $result);
    return $result;
  }

  function calculate_age($dob)
  {
      return date_diff(date_create($dob), date_create('today'))->y;
  }

  /**
   * Get district name by district ID
   *
   * @param int|null $district_id
   * @return string
   */
  function get_district_name($district_id)
  {
      if (!$district_id) {
          return 'No District Assigned';
      }

      $db = \Config\Database::connect();
      $builder = $db->table('districts');
      $district = $builder->where('id', $district_id)->get()->getRowArray();

      return $district ? $district['name'] : 'Unknown District';
  }

