<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);"><?= esc($org['name']) ?></li>
        </ol>
    </nav>

    <!-- Back <PERSON>ton and Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <img src="<?= imgcheck($org['orglogo']) ?>" alt="Logo" class="rounded-circle shadow-sm"
                         style="height: 64px; width: 64px; object-fit: cover; border: 3px solid var(--primary-color);">
                </div>
                <div>
                    <h3 class="mb-1 fw-bold text-light-text"><?= esc($org['name']) ?></h3>
                    <p class="text-secondary mb-0">
                        <i class="fas fa-fingerprint me-2"></i>Organization Code:
                        <span class="badge bg-lighter-bg text-light-text px-3 py-2 ms-1"><?= esc($org['orgcode']) ?></span>
                    </p>
                </div>
            </div>
        </div>
        <a href="<?= base_url('dakoii/dashboard') ?>" class="btn btn-outline-secondary text-light-text">
            <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
        </a>
    </div>

    <div class="row g-4">
        <!-- Organization Details Card -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-lighter-bg d-flex justify-content-between align-items-center py-3">
                    <h5 class="fw-bold mb-0 text-light-text"><i class="fas fa-building me-2"></i>Organization Details</h5>
                    <div>
                        <a href="<?= base_url('dakoii/organization/edit/' . $org['orgcode']) ?>" class="btn btn-sm btn-primary text-white">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                        <a href="<?= base_url('dakoii/organization/license/edit/' . $org['orgcode']) ?>" class="btn btn-sm btn-warning text-dark ms-1">
                            <i class="fas fa-key me-1"></i> License
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <!-- Organization Logo -->
                        <div class="col-md-4 text-center mb-4">
                            <div class="p-3 bg-lighter-bg rounded-3">
                                <img class="img-fluid rounded-3 shadow-sm" src="<?= imgcheck($org['orglogo']) ?>"
                                     alt="Organization Logo" style="max-width: 100%; max-height: 200px; object-fit: contain;">
                                <p class="text-secondary small mt-2 mb-0">Organization Logo</p>
                            </div>
                        </div>
                        <!-- Organization Info -->
                        <div class="col-md-8">
                            <div class="mb-4">
                                <h6 class="text-primary fw-bold mb-3 border-bottom pb-2">About Organization</h6>
                                <p class="text-light mb-0"><?= nl2br(esc($org['description'])) ?></p>
                            </div>

                            <h6 class="text-primary fw-bold mb-3 border-bottom pb-2">Location Settings</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card bg-lighter-bg border-0">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-globe text-primary me-2"></i>
                                                <h6 class="mb-0 text-light">Address Lock Country</h6>
                                            </div>
                                            <p class="mb-0 fw-medium text-light">
                                                <?= isset($country_name) ? esc($country_name) : '<span class="text-muted fst-italic">Not Set</span>' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-lighter-bg border-0">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                                <h6 class="mb-0 text-light">Address Lock Province</h6>
                                            </div>
                                            <p class="mb-0 fw-medium text-light">
                                                <?= isset($province_name) ? esc($province_name) : '<span class="text-muted fst-italic">Not Set</span>' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge bg-<?= $org['is_active'] ? 'success' : 'danger' ?> text-white me-2 px-3 py-2">
                                <i class="fas fa-<?= $org['is_active'] ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                            </span>
                            <span class="badge bg-<?= $org['license_status'] == 'paid' ? 'success' : 'warning' ?> <?= $org['license_status'] == 'paid' ? 'text-white' : 'text-dark' ?> px-3 py-2">
                                <i class="fas fa-<?= $org['license_status'] == 'paid' ? 'check-circle' : 'exclamation-circle' ?> me-1"></i>
                                License: <?= ucfirst($org['license_status']) ?>
                            </span>
                        </div>
                        <small class="text-dark">
                            <i class="fas fa-database me-1"></i> Organization ID: <?= $org['id'] ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Administrators Card -->
        <div class="col-lg-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-lighter-bg d-flex justify-content-between align-items-center py-3">
                    <h5 class="fw-bold mb-0 text-light-text"><i class="fas fa-user-shield me-2"></i>System Administrators</h5>
                    <a href="<?= base_url('dakoii/organization/admin/create/' . $org['orgcode']) ?>" class="btn btn-sm btn-success text-white">
                        <i class="fas fa-plus me-1"></i> New Admin
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-lighter-bg">
                                <tr>
                                    <th class="ps-3 text-light-text">Name</th>
                                    <th class="text-light-text">Role</th>
                                    <th class="text-light-text">Status</th>
                                    <th class="text-end pe-3 text-light-text"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($admins)): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-5">
                                        <div class="py-4">
                                            <i class="fas fa-user-shield text-muted opacity-25" style="font-size: 3rem;"></i>
                                            <h6 class="text-light-text mt-3">No Administrators Found</h6>
                                            <p class="text-muted mb-3 small">Create an administrator for this organization</p>
                                            <a href="<?= base_url('dakoii/organization/admin/create/' . $org['orgcode']) ?>" class="btn btn-sm btn-success text-white">
                                                <i class="fas fa-plus me-1"></i> Add Administrator
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($admins as $ur): ?>
                                <tr>
                                    <td class="ps-3">
                                        <div>
                                            <div class="fw-medium text-light-text"><?= esc($ur['name']) ?></div>
                                            <small class="text-muted"><i class="fas fa-user me-1"></i><?= esc($ur['username']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-white">
                                            <i class="fas fa-user-tag me-1"></i> <?= ucfirst(esc($ur['role'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= isset($ur['status']) && $ur['status'] == 1 ? 'success' : 'danger' ?> text-white">
                                            <i class="fas fa-<?= isset($ur['status']) && $ur['status'] == 1 ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                            <?= isset($ur['status']) && $ur['status'] == 1 ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td class="text-end pe-3">
                                        <a href="<?= base_url('dakoii/organization/admin/edit/' . $org['orgcode'] . '/' . $ur['id']) ?>" class="btn btn-sm btn-primary text-white">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organization Exercises -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="fw-bold mb-0 text-white"><i class="fas fa-clipboard-list me-2"></i>Organization Exercises</h5>
                        <?php if (isset($can_add_exercise) && $can_add_exercise): ?>
                        <a href="<?= base_url('dakoii/organization/exercise/create/' . $org['orgcode']) ?>" class="btn btn-sm btn-light">
                            <i class="fas fa-plus me-1"></i> Add Exercise
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="ps-3 text-dark">#</th>
                                    <th class="text-dark">Exercise Name</th>
                                    <th class="text-dark">Advertisement</th>
                                    <th class="text-dark">Date Range</th>
                                    <th class="text-dark">Status</th>
                                    <th class="text-dark text-end pe-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($exercises) && !empty($exercises)): ?>
                                    <?php $i = 1; foreach ($exercises as $exercise): ?>
                                    <tr class="bg-light">
                                        <td class="ps-3 text-dark"><?= $i++ ?></td>
                                        <td class="fw-medium text-dark"><?= esc($exercise['exercise_name']) ?></td>
                                        <td class="text-dark">
                                            <?php if (!empty($exercise['advertisement_no'])): ?>
                                                <span class="badge bg-primary text-white px-2 py-1">
                                                    <?= esc($exercise['advertisement_no']) ?>
                                                </span>
                                                <?php if (!empty($exercise['advertisement_date'])): ?>
                                                    <small class="text-muted d-block mt-1">
                                                        <?= date('d M Y', strtotime($exercise['advertisement_date'])) ?>
                                                    </small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted fst-italic">Not set</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-dark">
                                            <?php if(!empty($exercise['publish_date_from']) && !empty($exercise['publish_date_to'])): ?>
                                                <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?> -
                                                <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?>
                                            <?php else: ?>
                                                <span class="text-muted fst-italic">Not set</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = 'secondary';
                                            $statusIcon = 'circle';
                                            switch($exercise['status']) {
                                                case 'draft':
                                                    $statusClass = 'secondary';
                                                    $statusIcon = 'pencil-alt';
                                                    break;
                                                case 'publish':
                                                    $statusClass = 'success';
                                                    $statusIcon = 'check-circle';
                                                    break;
                                                case 'publish_request':
                                                    $statusClass = 'warning';
                                                    $statusIcon = 'clock';
                                                    break;
                                                case 'selection':
                                                    $statusClass = 'info';
                                                    $statusIcon = 'tasks';
                                                    break;
                                                case 'review':
                                                    $statusClass = 'primary';
                                                    $statusIcon = 'search';
                                                    break;
                                                case 'closed':
                                                    $statusClass = 'danger';
                                                    $statusIcon = 'times-circle';
                                                    break;
                                            }
                                            $textColor = ($statusClass == 'warning' || $statusClass == 'light') ? 'text-dark' : 'text-white';
                                            ?>
                                            <span class="badge bg-<?= $statusClass ?> <?= $textColor ?>">
                                                <i class="fas fa-<?= $statusIcon ?> me-1"></i>
                                                <?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?>
                                            </span>
                                        </td>
                                        <td class="text-end pe-3">
                                            <div class="btn-group">
                                                <a href="<?= base_url('dakoii/organization/exercise/view/' . $exercise['id']) ?>" class="btn btn-sm btn-info text-white" title="View Exercise">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= base_url('dakoii/organization/exercise/edit/' . $exercise['id']) ?>" class="btn btn-sm btn-warning text-white" title="Edit Exercise">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-primary text-white change-status-btn"
                                                        data-id="<?= $exercise['id'] ?>"
                                                        data-name="<?= esc($exercise['exercise_name']) ?>"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#changeStatusModal<?= $exercise['id'] ?>"
                                                        title="Change Status">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                <tr class="bg-light">
                                    <td colspan="6" class="text-center py-5">
                                        <div class="py-4">
                                            <i class="fas fa-clipboard-list text-muted opacity-25" style="font-size: 3rem;"></i>
                                            <h6 class="text-dark mt-3">No Exercises Found</h6>
                                            <p class="text-muted mb-0">There are no exercises for this organization yet.</p>
                                            <?php if (isset($can_add_exercise) && $can_add_exercise): ?>
                                            <a href="<?= base_url('dakoii/organization/exercise/create/' . $org['orgcode']) ?>" class="btn btn-sm btn-primary mt-3">
                                                <i class="fas fa-plus me-1"></i> Add Exercise
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exercise Status Change Modals -->
<?php if (isset($exercises) && !empty($exercises)): ?>
    <?php foreach ($exercises as $exercise): ?>
    <div class="modal fade" id="changeStatusModal<?= $exercise['id'] ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content bg-dark-bg">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-edit me-2"></i> Change Exercise Status
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <?= form_open('dakoii/organization/exercise/change-status/' . $exercise['id'] . '?redirect=org/' . $org['orgcode']) ?>
                <div class="modal-body p-4">
                    <!-- Add CSRF token -->
                    <?= csrf_field() ?>

                    <div class="text-center mb-4">
                        <div class="d-inline-block p-3 rounded-circle bg-primary bg-opacity-25 mb-3">
                            <i class="fas fa-clipboard-list text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <h5 class="text-light-text"><?= esc($exercise['exercise_name']) ?></h5>
                        <p class="text-secondary">Current Status:
                            <?php
                            $statusClass = 'secondary';
                            switch($exercise['status']) {
                                case 'draft': $statusClass = 'secondary'; break;
                                case 'publish': $statusClass = 'success'; break;
                                case 'publish_request': $statusClass = 'warning'; break;
                                case 'selection': $statusClass = 'info'; break;
                                case 'review': $statusClass = 'primary'; break;
                                case 'closed': $statusClass = 'danger'; break;
                            }
                            $textColor = ($statusClass == 'warning' || $statusClass == 'light') ? 'text-dark' : 'text-white';
                            ?>
                            <span class="badge bg-<?= $statusClass ?> <?= $textColor ?>">
                                <?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?>
                            </span>
                        </p>
                    </div>

                    <div class="mb-3">
                        <label for="status_<?= $exercise['id'] ?>" class="form-label text-light-text">New Status</label>
                        <select class="form-select form-select-lg" name="status" id="status_<?= $exercise['id'] ?>" required>
                            <option value="draft" <?= $exercise['status'] == 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="publish" <?= $exercise['status'] == 'publish' ? 'selected' : '' ?>>Publish</option>
                            <option value="publish_request" <?= $exercise['status'] == 'publish_request' ? 'selected' : '' ?>>Publish Request</option>
                            <option value="selection" <?= $exercise['status'] == 'selection' ? 'selected' : '' ?>>Selection</option>
                            <option value="review" <?= $exercise['status'] == 'review' ? 'selected' : '' ?>>Review</option>
                            <option value="closed" <?= $exercise['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                        </select>
                        <div class="form-text text-secondary mt-2">
                            <ul class="ps-3 mb-0">
                                <li><b>Draft</b>: Work in progress, not visible to public</li>
                                <li><b>Publish Request</b>: Pending approval for publication</li>
                                <li><b>Publish</b>: Publicly visible exercise</li>
                                <li><b>Selection</b>: In selection phase</li>
                                <li><b>Review</b>: Under review</li>
                                <li><b>Closed</b>: Exercise is closed and archived</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary text-light-text" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary text-white">
                        <i class="fas fa-save me-1"></i> Update Status
                    </button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
<?php endif; ?>

<?= $this->endSection() ?>